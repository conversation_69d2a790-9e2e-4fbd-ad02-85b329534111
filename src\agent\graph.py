"""LangGraph chatbot graph with checkpointing.

A simple chatbot that uses OpenRouter LLM via llm_factory with conversation persistence.
"""

from __future__ import annotations

import sys
from pathlib import Path



# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import Any, Dict, TypedDict
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.checkpoint.memory import MemorySaver

from agent.nodes import call_model
from agent.nodes.tools_node import execute_tools
from agent.state import State
from langchain_core.messages import AIMessage

from agent.stub import CustomAgent
from typing_extensions import TypedDict

# class Configuration(TypedDict):
#     """Configurable parameters for the chatbot."""
#     model_name: str
#     system_prompt: str


# # Create checkpointer for conversation persistence
# checkpointer = MemorySaver()

# # Define the graph with checkpointing
# graph = (
#     StateGraph(State, config_schema=Configuration)
#     .add_node("call_model", call_model)
#     .add_edge("__start__", "call_model")
#     .compile(name="VoiceAgentALL")
# )


async def mcp_tools(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Execute MCP tools based on tool calls in the last message.

    Args:
        state: Current conversation state
        config: Runtime configuration

    Returns:
        Updated state with tool execution results
    """
    print("In node: mcp_tools")
    return await execute_tools(state, config)


def conditional_edge_1(state: State) -> str:
    """Conditional edge to determine if tools should be called.

    Checks if the last message contains tool calls that need to be executed.

    Args:
        state: Current conversation state

    Returns:
        "__end__" if no tool calls, "mcp_tools" if tool calls are present
    """
    print("In condition: conditional_edge_1")

    # Get the last message from the conversation
    messages = state.get("messages", [])
    if not messages:
        return "__end__"

    last_message = messages[-1]

    # Check if the last message is an AI message with tool calls
    if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        print(f"Found {len(last_message.tool_calls)} tool calls, routing to mcp_tools")
        return "mcp_tools"

    print("No tool calls found, routing to __end__")
    return "__end__"


agent = CustomAgent(
    state_schema=State,
    impl=[
        ("call_model", call_model),
        ("mcp_tools", mcp_tools),
        ("conditional_edge_1", conditional_edge_1),  
    ],
)

graph = agent.compile()


