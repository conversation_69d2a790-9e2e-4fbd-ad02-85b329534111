# This YAML was auto-generated based on an architecture 
# designed in LangGraph Builder (https://build.langchain.com).
#
# The YAML was used by langgraph-gen (https://github.com/langchain-ai/langgraph-gen-py) 
# to generate a code stub for a LangGraph application that follows the architecture.
#
# langgraph-gen is an open source CLI tool that converts YAML specifications into LangGraph code stubs.
#
# The code stub generated from this YAML can be found in stub.py.
#
# A placeholder implementation for the generated stub can be found in implementation.py.

name: CustomAgent
nodes:
  - name: call_model
  - name: mcp_tools
edges:
  - from: __start__
    to: call_model
  - from: mcp_tools
    to: call_model
  - from: call_model
    condition: conditional_edge_1
    paths: [__end__, mcp_tools]